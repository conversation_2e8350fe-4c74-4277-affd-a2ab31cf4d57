QT       += core gui sql

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    utils/databasemanager.cpp \
    utils/emojimanager.cpp \
    model/user.cpp \
    model/contact.cpp \
    model/message.cpp \
    model/group.cpp \
    dao/userdao.cpp \
    dao/contactdao.cpp \
    dao/messagedao.cpp \
    dao/groupdao.cpp \
    service/userservice.cpp \
    service/contactservice.cpp \
    service/messageservice.cpp \
    service/groupservice.cpp \
    view/logindialog.cpp \
    view/registerdialog.cpp \
    view/addfrienddialog.cpp \
    view/friendlistwidget.cpp \
    view/grouplistwidget.cpp \
    view/messagebubble.cpp \
    view/emojipanel.cpp \
    view/chatwidget.cpp \
    view/creategroupdialog.cpp

HEADERS += \
    mainwindow.h \
    utils/databasemanager.h \
    utils/emojimanager.h \
    model/user.h \
    model/contact.h \
    model/message.h \
    model/group.h \
    dao/userdao.h \
    dao/contactdao.h \
    dao/messagedao.h \
    dao/groupdao.h \
    service/userservice.h \
    service/contactservice.h \
    service/messageservice.h \
    service/groupservice.h \
    view/logindialog.h \
    view/registerdialog.h \
    view/addfrienddialog.h \
    view/friendlistwidget.h \
    view/grouplistwidget.h \
    view/messagebubble.h \
    view/emojipanel.h \
    view/chatwidget.h \
    view/creategroupdialog.h

FORMS += \
    mainwindow.ui \
    view/logindialog.ui \
    view/registerdialog.ui \
    view/addfrienddialog.ui \
    view/friendlistwidget.ui \
    view/grouplistwidget.ui \
    view/creategroupdialog.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
