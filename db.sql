-- 即时通讯聊天系统数据库设计
-- 数据库：ChatSystem
-- 服务器：DESKTOP-FJGV90L\SQLEXPRESS

-- 创建数据库
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'ChatSystem')
BEGIN
    CREATE DATABASE ChatSystem;
END
GO

USE ChatSystem;
GO

-- 1. 用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        user_id INT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        nickname NVARCHAR(100) NOT NULL,
        avatar NVARCHAR(255) DEFAULT 'default_avatar.png',
        status NVARCHAR(20) DEFAULT 'offline', -- online, offline, busy
        created_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- 2. 联系人表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='contacts' AND xtype='U')
BEGIN
    CREATE TABLE contacts (
        contact_id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        friend_id INT NOT NULL,
        created_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (user_id) REFERENCES users(user_id),
        FOREIGN KEY (friend_id) REFERENCES users(user_id),
        UNIQUE(user_id, friend_id)
    );
END
GO

-- 3. 群组表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='groups' AND xtype='U')
BEGIN
    CREATE TABLE groups (
        group_id INT IDENTITY(1,1) PRIMARY KEY,
        group_name NVARCHAR(100) NOT NULL,
        group_avatar NVARCHAR(255) DEFAULT 'default_group.png',
        creator_id INT NOT NULL,
        created_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (creator_id) REFERENCES users(user_id)
    );
END
GO

-- 4. 群组成员表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='group_members' AND xtype='U')
BEGIN
    CREATE TABLE group_members (
        member_id INT IDENTITY(1,1) PRIMARY KEY,
        group_id INT NOT NULL,
        user_id INT NOT NULL,
        joined_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (group_id) REFERENCES groups(group_id),
        FOREIGN KEY (user_id) REFERENCES users(user_id),
        UNIQUE(group_id, user_id)
    );
END
GO

-- 5. 文件表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='files' AND xtype='U')
BEGIN
    CREATE TABLE files (
        file_id INT IDENTITY(1,1) PRIMARY KEY,
        file_name NVARCHAR(255) NOT NULL,
        file_path NVARCHAR(500) NOT NULL,
        file_size BIGINT NOT NULL,
        file_type NVARCHAR(50) NOT NULL, -- image, document, video, etc.
        uploaded_at DATETIME DEFAULT GETDATE()
    );
END
GO

-- 6. 消息表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='messages' AND xtype='U')
BEGIN
    CREATE TABLE messages (
        message_id INT IDENTITY(1,1) PRIMARY KEY,
        sender_id INT NOT NULL,
        receiver_id INT NULL, -- 私聊时使用
        group_id INT NULL, -- 群聊时使用
        message_type NVARCHAR(20) NOT NULL, -- text, image, file, emoji
        content NTEXT NULL, -- 文本消息内容或表情包代码
        file_id INT NULL, -- 文件消息时关联文件表
        sent_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (sender_id) REFERENCES users(user_id),
        FOREIGN KEY (receiver_id) REFERENCES users(user_id),
        FOREIGN KEY (group_id) REFERENCES groups(group_id),
        FOREIGN KEY (file_id) REFERENCES files(file_id)
    );
END
GO

-- 插入示例数据

-- 插入用户数据
INSERT INTO users (username, password, nickname, status) VALUES
('alice', 'password123', '爱丽丝', 'online'),
('bob', 'password456', '鲍勃', 'online'),
('charlie', 'password789', '查理', 'offline'),
('diana', 'passwordabc', '戴安娜', 'busy');
GO

-- 插入联系人关系
INSERT INTO contacts (user_id, friend_id) VALUES
(1, 2), (2, 1), -- alice和bob互为好友
(1, 3), (3, 1), -- alice和charlie互为好友
(2, 4), (4, 2); -- bob和diana互为好友
GO

-- 插入群组
INSERT INTO groups (group_name, creator_id) VALUES
('技术讨论群', 1),
('朋友聚会', 2);
GO

-- 插入群组成员
INSERT INTO group_members (group_id, user_id) VALUES
(1, 1), (1, 2), (1, 3), -- 技术讨论群成员
(2, 2), (2, 4); -- 朋友聚会成员
GO

-- 插入文件记录
INSERT INTO files (file_name, file_path, file_size, file_type) VALUES
('头像1.jpg', '/uploads/images/avatar1.jpg', 102400, 'image'),
('文档.pdf', '/uploads/documents/doc1.pdf', 2048000, 'document'),
('表情包.gif', '/uploads/emojis/emoji1.gif', 51200, 'image');
GO

-- 插入消息记录
INSERT INTO messages (sender_id, receiver_id, message_type, content) VALUES
(1, 2, 'text', '你好，鲍勃！'),
(2, 1, 'text', '嗨，爱丽丝！最近怎么样？'),
(1, 2, 'emoji', ':smile:'),
(2, 1, 'text', '我很好，谢谢！');

INSERT INTO messages (sender_id, group_id, message_type, content) VALUES
(1, 1, 'text', '大家好！欢迎来到技术讨论群'),
(2, 1, 'text', '谢谢群主！'),
(3, 1, 'text', '很高兴加入这个群');

INSERT INTO messages (sender_id, receiver_id, message_type, file_id) VALUES
(1, 2, 'image', 1);
GO

-- 创建索引以提高查询性能
CREATE INDEX IX_messages_sender_id ON messages(sender_id);
CREATE INDEX IX_messages_receiver_id ON messages(receiver_id);
CREATE INDEX IX_messages_group_id ON messages(group_id);
CREATE INDEX IX_messages_sent_at ON messages(sent_at);
GO

PRINT '数据库创建完成！';
PRINT '包含表：users, contacts, groups, group_members, files, messages';
PRINT '已插入示例数据，可以开始测试聊天系统功能。';
